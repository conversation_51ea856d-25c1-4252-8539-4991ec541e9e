import { Form<PERSON>rovider, useFieldArray, useForm } from "react-hook-form";
import { FaCheckCircle } from "react-icons/fa";
import { useEffect } from "react";

import { ENV_TYPE } from "@/app/app.constants";
import { useGetRecipes } from "@/app/services/recipes.hook";

import { AppButton } from "../app-button";
import { AppInput } from "../app-input";
import { AppSpinner } from "../app-spinner";
import { JobTypeFuzzer } from "../job-type-fuzzer";
import { SubFormEchidna, SubFormEchidnaAdvanced } from "./subform-echidna";
import { SubFormFoundry, SubFormFoundryAdvanced } from "./subform-foundry";
import { SubFormHalmos, SubFormHalmosAdvanced } from "./subform-halmos";
import { SubFormKontrol, SubFormKontrolAdvanced } from "./subform-kontrol";
import { SubFormMedusa, SubFormMedusaAdvanced } from "./subform-medusa";

import type { JobFormProps, GitHubLinkFormValues } from "./types";
import { parseGitHubURL, applyRecipeDefaults } from "./utils";
import { FORM_STYLES } from "./constants";

export function CreateJobForm({
  onSubmit,
  title,
  submitLabel = "Start Job",
  hideEnv = false,
  hidePresets = false,
  env,
  setEnv,
  jobId,
  dynamicReplacement,
  buildHandler,
  setRecipeId,
}: JobFormProps) {
  const {
    register,
    handleSubmit,
    setValue,
    setError,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<GitHubLinkFormValues>({
    defaultValues: {
      timeout: "3600",
      testLimit: "100000",
    },
  });
  const { data: recipes } = useGetRecipes();

  const orgName = watch("orgName");
  const repoName = watch("repoName");
  const ref = watch("ref");

  const methods = useForm<GitHubLinkFormValues>({
    defaultValues: {
      fields: [{ variableName: "", interface: "", value: "" }],
    },
  });
  const { control } = methods;

  const { fields, append, remove } = useFieldArray({
    control,
    name: "fields",
  });

  const watchedFields = watch("fields");

  useEffect(() => {
    // Filter out incomplete field groups
    if (!watchedFields) return;

    const validFields = watchedFields.filter(
      (field) =>
        field.variableName.trim() &&
        field.interface.trim() &&
        field.value.trim()
    );

    const prepContract = validFields.map((field) => ({
      target: `${field.variableName} = ${field.interface}`,
      replacement: `${field.variableName} = ${field.interface}(${field.value});`,
      endOfTargetMarker: "[^;]*",
      targetContract: "Setup.sol",
    }));
    setValue("prepareContracts", prepContract);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watchedFields, methods.getValues()]);

  const githubUrlRegister = register("githubURL");

  return (
    <FormProvider
      {...({
        register,
        handleSubmit,
        setValue,
        setError,
        watch,
        errors,
        isSubmitting,
      } as any)}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <h3 className="mb-[22px] text-[28px] leading-[33px] text-fore-neutral-primary">
          {title}
        </h3>

        {!hidePresets && recipes && (
          <div className="my-[20px] flex flex-wrap gap-[20px]">
            {recipes.map((recipe) => (
              <AppButton
                onClick={() =>
                  applyRecipeDefaults(recipe, setValue, setEnv, setRecipeId)
                }
                className=" px-[14px] py-[9px] leading-[21px]"
                key={recipe.displayName}
              >
                Use Default `{recipe.displayName}`
              </AppButton>
            ))}
          </div>
        )}

        {!hideEnv && env && (
          <div className="mb-6 w-full">
            <JobTypeFuzzer value={env} onChange={(value) => setEnv(value)} />
          </div>
        )}

        <div className={FORM_STYLES.formContainer}>
          <div className="space-y-6">
            {!buildHandler && (
              <div className={FORM_STYLES.inputGroupSingle}>
                <div className={FORM_STYLES.fieldContainer}>
                  <AppInput
                    className={FORM_STYLES.input}
                    label="Job Label"
                    {...register("label")}
                    type="text"
                    defaultValue=""
                  />
                </div>
              </div>
            )}

            <div className={FORM_STYLES.inputGroupSingle}>
              <div className={FORM_STYLES.fieldContainer}>
                <AppInput
                  className={FORM_STYLES.input}
                  {...githubUrlRegister}
                  onChange={(e) => {
                    githubUrlRegister.onChange(e);
                    parseGitHubURL(e.target.value, setValue, setError);
                  }}
                  type="text"
                  label="GitHub Repo URL"
                  placeholder="Enter GitHub Repo URL"
                  error={errors.githubURL?.message}
                />
              </div>
            </div>
          </div>

          <div className={FORM_STYLES.divider} />

          <div className="space-y-4">
            <h3 className={FORM_STYLES.sectionTitle}>
              Or specify the organization, repository and branch directly
            </h3>

            <div className="space-y-6">
              <div className={FORM_STYLES.inputGroup}>
                <div className={FORM_STYLES.fieldContainer}>
                  <AppInput
                    className={FORM_STYLES.input}
                    label="Organization"
                    {...register("orgName")}
                    type="text"
                  />
                </div>
                <div className={FORM_STYLES.fieldContainer}>
                  <AppInput
                    className={FORM_STYLES.input}
                    {...register("repoName")}
                    type="text"
                    label="Repo"
                  />
                </div>
              </div>

              <div className={FORM_STYLES.inputGroup}>
                <div className={FORM_STYLES.fieldContainer}>
                  <AppInput
                    className={FORM_STYLES.input}
                    {...register("ref")}
                    type="text"
                    label="Branch"
                  />
                </div>
                <div className={FORM_STYLES.fieldContainer}>
                  <AppInput
                    className={FORM_STYLES.input}
                    {...register("directory")}
                    type="text"
                    label="Directory"
                  />
                </div>
              </div>
            </div>
          </div>

          {!env && (
            <div className={FORM_STYLES.inputGroupSingle}>
              <div className={FORM_STYLES.fieldContainer}>
                <AppInput
                  className={FORM_STYLES.input}
                  {...register("customOut")}
                  type="text"
                  label="Custom output folder"
                />
              </div>
            </div>
          )}

          {dynamicReplacement && (
            <div className="space-y-4">
              <h3 className={FORM_STYLES.sectionTitle}>Dynamic replacement</h3>

              <div className="space-y-4">
                {fields.map((field, index) => (
                  <div key={field.id} className={FORM_STYLES.inputGroupTriple}>
                    <div className={FORM_STYLES.fieldContainer}>
                      <AppInput
                        className={FORM_STYLES.input}
                        label="Variable Name"
                        {...register(`fields.${index}.variableName` as const)}
                        type="text"
                        defaultValue={field.variableName}
                      />
                    </div>
                    <div className={FORM_STYLES.fieldContainer}>
                      <AppInput
                        className={FORM_STYLES.input}
                        label="Interface"
                        {...register(`fields.${index}.interface` as const)}
                        type="text"
                        defaultValue={field.interface}
                      />
                    </div>
                    <div className={FORM_STYLES.fieldContainer}>
                      <AppInput
                        className={FORM_STYLES.input}
                        label="Value"
                        {...register(`fields.${index}.value` as const)}
                        type="text"
                        defaultValue={field.value}
                      />
                      <button
                        type="button"
                        onClick={() => remove(index)}
                        className="mt-2 text-sm text-accent-primary underline hover:text-accent-secondary"
                        title="Delete Field Group"
                      >
                        Remove
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              <AppButton
                type="button"
                onClick={() =>
                  append({ variableName: "", interface: "", value: "" })
                }
                className="mt-4"
              >
                Add More Fields
              </AppButton>
            </div>
          )}

          <div className={FORM_STYLES.divider} />

          {!hideEnv && env && (
            <div className="space-y-4">
              <h3 className={FORM_STYLES.sectionTitle}>
                Configure custom parameters for {env.toLowerCase()}:
              </h3>

              <div className="space-y-8">
                {env === ENV_TYPE.MEDUSA ? (
                  <SubFormMedusa />
                ) : env === ENV_TYPE.ECHIDNA ? (
                  <SubFormEchidna />
                ) : env === ENV_TYPE.FOUNDRY ? (
                  <SubFormFoundry />
                ) : env === ENV_TYPE.HALMOS ? (
                  <SubFormHalmos />
                ) : (
                  <SubFormKontrol />
                )}
              </div>
            </div>
          )}

          <div className={FORM_STYLES.divider} />
          {!hideEnv && env && (
            <div className="space-y-4">
              <h3 className={FORM_STYLES.sectionTitle}>
                Advanced Configuration:
              </h3>

              <div className="space-y-8">
                {env === ENV_TYPE.MEDUSA ? (
                  <SubFormMedusaAdvanced />
                ) : env === ENV_TYPE.ECHIDNA ? (
                  <SubFormEchidnaAdvanced />
                ) : env === ENV_TYPE.FOUNDRY ? (
                  <SubFormFoundryAdvanced />
                ) : env === ENV_TYPE.HALMOS ? (
                  <SubFormHalmosAdvanced />
                ) : (
                  <SubFormKontrolAdvanced />
                )}
              </div>
            </div>
          )}

          <div className={FORM_STYLES.divider} />

          <div className={FORM_STYLES.inputGroupSingle}>
            <div className={FORM_STYLES.fieldContainer}>
              <AppButton
                type="submit"
                disabled={isSubmitting}
                size="lg"
                fullWidth
                className="flex flex-row items-center justify-center gap-1"
              >
                {isSubmitting ? <AppSpinner /> : submitLabel}
              </AppButton>
            </div>
          </div>

          {!!jobId && (
            <div className="space-y-4">
              <h3 className={FORM_STYLES.sectionTitle}>Job Details:</h3>
              <div className="flex items-center justify-center gap-3 rounded-lg bg-status-success p-4 text-lg leading-[21px] text-fore-neutral-primary">
                <FaCheckCircle className="size-5 text-fore-neutral-primary" />
                Job has been successfully created
              </div>
              <p className="mb-4 text-base leading-[18px] text-fore-neutral-primary">
                ID: {jobId}
              </p>

              {[
                `Org: ${orgName ?? ""}`,
                `Repo: ${repoName ?? ""}`,
                `Branch: ${ref ?? ""}`,
                `Directory: ${watch("directory") ?? ""}`,
                `Custom Out: ${watch("customOut") ?? ""}`,
              ]
                .filter(Boolean)
                .map((line) => (
                  <p
                    key={line}
                    className="mb-1 w-full border-b border-stroke-neutral-decorative pb-4 text-base leading-[18px] text-fore-neutral-secondary"
                  >
                    {line}
                  </p>
                ))}
            </div>
          )}
        </div>
      </form>
    </FormProvider>
  );
}
