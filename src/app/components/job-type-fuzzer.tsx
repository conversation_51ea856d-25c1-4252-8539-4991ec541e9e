"use client";

import { cn } from "../helpers/cn";
import { ENV_TYPE } from "../app.constants";

type JobTypeFuzzerProps = {
  value: ENV_TYPE;
  onChange: (value: ENV_TYPE) => void;
  className?: string;
};

const jobTypeOptions = [
  { label: "Medusa", value: ENV_TYPE.MEDUSA },
  { label: "Echidna", value: ENV_TYPE.ECHIDNA },
  { label: "Foundry", value: ENV_TYPE.FOUNDRY },
  { label: "Halmos", value: ENV_TYPE.HALMOS },
  { label: "Kontrol", value: ENV_TYPE.KONTROL },
];

export const JobTypeFuzzer = ({
  value,
  onChange,
  className,
}: JobTypeFuzzerProps) => {
  return (
    <div
      className={cn(
        "flex flex-col gap-3 pb-4 border-b border-back-neutral-tertiary",
        className
      )}
    >
      <div className="flex flex-row items-stretch justify-stretch gap-2.5 self-stretch">
        <h3 className="text-xl font-bold leading-[1.3] flex-1 text-fore-neutral-primary">
          Select Job Type
        </h3>
      </div>

      <div className="flex flex-row flex-wrap gap-3">
        {jobTypeOptions.map((option) => {
          const isSelected = value === option.value;

          return (
            <button
              key={option.value}
              type="button"
              onClick={() => onChange(option.value)}
              className={cn(
                "flex flex-row justify-center items-center gap-2.5 px-1.5 py-1.5 rounded-lg transition-all duration-200",
                "text-sm font-black leading-[1.57]",
                isSelected
                  ? "bg-back-accent-primary text-accent-primary"
                  : "border border-fore-neutral-tertiary text-fore-neutral-tertiary hover:border-fore-neutral-primary hover:text-fore-neutral-primary"
              )}
            >
              {option.label}
            </button>
          );
        })}
      </div>
    </div>
  );
};
