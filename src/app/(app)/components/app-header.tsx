"use client";

import { Fa<PERSON><PERSON>, FaSun } from "react-icons/fa";

import { useGetMyOrg } from "@/app/services/organization.hooks";
import { THEME_OPTIONS } from "@/app/services/ThemeProvider";
import { usePersistedTheme } from "@/app/services/useThemePersistence";

import { cn } from "@/lib/utils";
import { AppLogo } from "../../components/app-logo";
import { AppUser } from "./app-user";
import Link from "next/link";

function ThemeToggle({
  isDark,
  onThemeChange,
}: {
  isDark: boolean;
  onThemeChange: (isDark: boolean) => void;
}) {
  const knobPosition = isDark ? "left-[calc(100%-28px)]" : "left-1";

  const btnBase =
    "relative z-10 flex h-6 w-6 items-center justify-center rounded-full transition-all duration-300 ease-in-out hover:scale-110";

  return (
    <div className="theme-switch-container relative ml-auto mr-2 flex min-h-[32px] min-w-[68px] items-center justify-center rounded-full border border-stroke-neutral-decorative bg-back-neutral-secondary p-1 text-fore-neutral-primary shadow-sm transition-all duration-200 hover:shadow-md">
      <div
        className={cn(
          "absolute top-1 h-6 w-6 rounded-full bg-back-accent-primary shadow-sm transition-all duration-300 ease-in-out",
          {
            [knobPosition]: true,
          }
        )}
      />

      <button
        onClick={() => onThemeChange(true)}
        className={cn(btnBase, { "border border-primary": !isDark })}
        aria-label="Switch to light theme"
      >
        <FaSun className={cn("size-3 transition-transform duration-300")} />
      </button>

      <button
        onClick={() => onThemeChange(false)}
        className={cn(btnBase, { "border border-primary": isDark })}
        aria-label="Switch to dark theme"
      >
        <FaMoon className={cn("size-3 transition-transform duration-300")} />
      </button>
    </div>
  );
}

export const AppHeader = ({ skipUser }: { skipUser?: boolean }) => {
  const {
    data: organization,
    isLoading,
    orgStatus,
  } = useGetMyOrg({
    redirectOnFail: false,
  });
  const { setTheme, isDark } = usePersistedTheme();

  const onThemeChange = (isDark: boolean) => {
    setTheme(!isDark ? THEME_OPTIONS.dark : THEME_OPTIONS.light);
  };

  return (
    <header className="sticky-header-blur sticky top-0 z-50 flex w-full items-center justify-between border-b border-stroke-neutral-decorative bg-back-neutral-secondary dark:bg-back-neutral-primary px-6 py-4 backdrop-blur-sm">
      <Link href="/" className="cursor-pointer">
        <div className="flex items-center">
          <AppLogo />
        </div>
      </Link>

      <ThemeToggle isDark={isDark} onThemeChange={onThemeChange} />

      {!skipUser && (
        <AppUser
          {...{
            organization,
            isLoading,
            orgStatus,
          }}
        />
      )}
    </header>
  );
};
